package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.HAS_OVERLAPPING_REBATES;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_ITEM_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_REBATE_NOT_FOUND;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.mapper.itemvendorrebate.ItemVendorRebateDtoApplicationMapper;
import com.mercaso.ims.application.service.ItemVendorRebateApplicationService;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateFactory;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Application service for ItemVendorRebate operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ItemVendorRebateApplicationServiceImpl implements ItemVendorRebateApplicationService {

    private final ItemVendorRebateService itemVendorRebateService;
    private final ItemVendorRebateFactory itemVendorRebateFactory;
    private final VendorItemService vendorItemService;
    private final ItemVendorRebateDtoApplicationMapper itemVendorRebateDtoApplicationMapper;

    /**
     * Create a new item vendor rebate using command
     * @param command the create command
     * @return the created rebate DTO
     */
    @Override
    public ItemVendorRebateDto createRebate(CreateItemVendorRebateCommand command) {

        UUID vendorItemId = command.getVendorItemId();

        VendorItem vendorItem = vendorItemService.findById(vendorItemId);
        if (null == vendorItem) {
            log.error("Vendor item not foung vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        log.info("Creating new ItemVendorRebate for vendorItemId: {}", vendorItemId);

        // Get rebates for vendor item - implemented in memory for better performance
        List<ItemVendorRebate> itemVendorRebates = itemVendorRebateService.findByVendorItemId(vendorItemId);

        // Check for overlapping rebates using in-memory logic equivalent to the SQL query
        boolean hasOverlappingRebates = hasOverlappingRebates(
            itemVendorRebates,
            command.getStartDate(),
            command.getEndDate()
        );

        // Throw exception if overlapping rebates found
        if (hasOverlappingRebates) {
            log.error("Rebate schedule conflicts with existing rebates, vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(HAS_OVERLAPPING_REBATES);
        }

        ItemVendorRebate rebate = itemVendorRebateFactory.create(
                command.getVendorItemId(),
                command.getVendorId(),
                command.getItemId(),
                command.getStartDate(),
                command.getEndDate(),
                command.getRebatePerUnit()
        );

        ItemVendorRebate savedRebate = itemVendorRebateService.save(rebate);


        return itemVendorRebateDtoApplicationMapper.domainToDto(savedRebate);
    }

    /**
     * Update an existing item vendor rebate using command
     * @param command the update command
     * @return the updated rebate DTO
     */
    @Override
    public ItemVendorRebateDto updateRebate(UpdateItemVendorRebateCommand command) {

        UUID rebateId = command.getId();

        // Find the existing rebate
        ItemVendorRebate existingRebate = itemVendorRebateService.findById(rebateId);
        if (null == existingRebate) {
            log.error("Vendor rebate not found, rebateId:{}", rebateId);
            throw new ImsBusinessException(VENDOR_REBATE_NOT_FOUND);
        }

        log.info("Updating ItemVendorRebate with id: {}", rebateId);

        // Validate vendor item exists
        UUID vendorItemId = command.getVendorItemId();
        VendorItem vendorItem = vendorItemService.findById(vendorItemId);
        if (null == vendorItem) {
            log.error("Vendor item not found vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        // Get all rebates for vendor item and exclude the current one being updated
        List<ItemVendorRebate> allItemVendorRebates = itemVendorRebateService.findByVendorItemId(vendorItemId);
        List<ItemVendorRebate> itemVendorRebates = allItemVendorRebates
                .stream()
                .filter(rebate -> !rebate.getId().equals(command.getId()))
                .toList();


        // Check for overlapping rebates with other rebates (excluding current one)
        boolean hasOverlappingRebates = hasOverlappingRebates(
            itemVendorRebates,
            command.getStartDate(),
            command.getEndDate()
        );

        // Throw exception if overlapping rebates found
        if (hasOverlappingRebates) {
            log.error("Updated rebate schedule conflicts with existing rebates, vendorItemId:{}", vendorItemId);
            throw new ImsBusinessException(HAS_OVERLAPPING_REBATES);
        }

        // Update the rebate properties
        existingRebate.setStartDate(command.getStartDate());
        existingRebate.setEndDate(command.getEndDate());
        existingRebate.setRebatePerUnit(command.getRebatePerUnit());
        if (command.getItemVendorRebateStatus() != null) {
            existingRebate.setItemVendorRebateStatus(command.getItemVendorRebateStatus());
        }

        // Save the updated rebate
        ItemVendorRebate updatedRebate = itemVendorRebateService.update(existingRebate);

        return itemVendorRebateDtoApplicationMapper.domainToDto(updatedRebate);
    }

    @Override
    public void deleteRebate(UUID id) {
        itemVendorRebateService.delete(id);
    }

    /**
     * Check if two rebates overlap using the same logic as the SQL query
     * Equivalent to the SQL query logic for overlap detection
     * @param existingRebate the existing rebate
     * @param newStartDate the new rebate's start date
     * @param newEndDate the new rebate's end date (can be null for continuous)
     * @return true if rebates overlap
     */
    private boolean checkRebateOverlap(ItemVendorRebate existingRebate,
                                       LocalDate newStartDate, LocalDate newEndDate) {
        // Extract existing rebate parameters
        LocalDate existingStartDate = existingRebate.getStartDate();
        LocalDate existingEndDate = existingRebate.getEndDate();
        UUID vendorItemId = existingRebate.getVendorItemId();


        // Case 1: Both new and existing rebates are continuous (both endDate are null)
        if (newEndDate == null || existingEndDate == null) {
            log.warn("Found overlapping continuous rebates for vendorItemId: {}", vendorItemId);
            return true;
        }

        // Case 2: New rebate is continuous (newEndDate is null), existing has end date
        if (existingEndDate.isAfter(newStartDate) && existingStartDate.isBefore(newEndDate)) {
            log.warn("New continuous rebate overlaps with existing fixed rebate starting:{}, ending: {}, vendorItemId:{}",
                    existingStartDate,
                    existingEndDate,
                    vendorItemId);
            return true;
        }

        return false;
    }

    /**
     * Check for overlapping rebates in memory using stream operations
     * @param existingRebates list of existing rebates to check against
     * @param newStartDate the new rebate's start date
     * @param newEndDate the new rebate's end date (can be null for continuous)
     * @return true if any overlapping rebates found
     */
    private boolean hasOverlappingRebates(List<ItemVendorRebate> existingRebates, LocalDate newStartDate, LocalDate newEndDate) {
        return existingRebates.stream().anyMatch(existingRebate ->
            checkRebateOverlap(existingRebate, newStartDate, newEndDate));
    }
}
