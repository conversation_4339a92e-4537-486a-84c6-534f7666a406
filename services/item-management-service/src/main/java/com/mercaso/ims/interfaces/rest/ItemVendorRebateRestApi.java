package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.service.ItemVendorRebateApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;


/**
 * REST Controller for ItemVendorRebate operations
 * Manages rebates for direct suppliers:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if empty, rebate is continuous and valid indefinitely
 * - Supplier: Must be direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/item-vendor-rebates")
@RequiredArgsConstructor
@Tag(name = "Item Vendor Rebate", description = "Direct Supplier Rebate management APIs")
public class ItemVendorRebateRestApi {

    private final ItemVendorRebateApplicationService itemVendorRebateApplicationService;

    @PostMapping
    @Operation(summary = "Create a new direct supplier rebate",
               description = "Creates a rebate for a direct supplier. Start Date is required, End Date is optional (null means continuous rebate).")
    @PreAuthorize("hasAuthority('ims:write:item-vendor-rebates')")
    public ItemVendorRebateDto createRebate(@RequestBody CreateItemVendorRebateCommand command) {
        log.info("Creating new item vendor rebate for vendorItemId: {}", command.getVendorItemId());

        Assert.isTrue(command.isValid(), command.getValidationError());

        return itemVendorRebateApplicationService.createRebate(command);
    }

    @PutMapping
    @Operation(summary = "Update the direct supplier rebate",
            description = "Updates a rebate for a direct supplier. Start Date is required, End Date is optional (null means continuous rebate).")
    @PreAuthorize("hasAuthority('ims:write:item-vendor-rebates')")
    public ItemVendorRebateDto updateRebate(@RequestBody UpdateItemVendorRebateCommand command) {
        log.info("Updating new item vendor rebate for vendorItemId: {}", command.getVendorItemId());

        Assert.isTrue(command.isValid(), command.getValidationError());

        return itemVendorRebateApplicationService.updateRebate(command);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete the direct supplier rebate",
            description = "Deletes a rebate for a direct supplier. Start Date is required, End Date is optional (null means continuous rebate).")
    @PreAuthorize("hasAuthority('ims:write:item-vendor-rebates')")
    public void deleted(@PathVariable("id") UUID id) {
        log.info("Deleting new item vendor rebate for id: {}", id);

        itemVendorRebateApplicationService.deleteRebate(id);
    }
}
