package com.mercaso.ims.application.service;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;

import java.util.UUID;

/**
 * Application service for ItemVendorRebate operations
 */
public interface ItemVendorRebateApplicationService {

    ItemVendorRebateDto createRebate(CreateItemVendorRebateCommand command);

    ItemVendorRebateDto updateRebate(UpdateItemVendorRebateCommand command);

    void deleteRebate(UUID id);
}
