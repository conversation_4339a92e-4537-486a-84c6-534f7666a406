host:
  db_server: postgres-ims:5432
spring:
  config:
    activate:
      on-profile: integration
  datasource:
    username: item_management_user
    password: mercaso
    url: jdbc:postgresql://${host.db_server}/item_management_service
  cloud:
    vault:
      enabled: false
  ai:
    openai:
      api-key: sk
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.4

otel:
  exporter:
    otlp:
      endpoint: http://localhost:4317

security:
  enable-method-security: false
  public-paths:
    - /**
mercaso:
  document:
    operations:
      storage:
        bucket-name: mercaso-ims
  featureflags:
    sdk: test
shopify:
  host:
  access_token: test_access_token
  query_product_url:
  create_product_url:
  modify_product_url:
  delete_product_url:
  create_product_meta_field_url:
  delete_product_meta_field_url:
  query_product_meta_field_url:
  modify_product_meta_field_url:
  modify_product_inventory_item_url:
  set_product_channel_url:
finale:
  accountPathComponent: mercasosandbox
  token:


googleDriver:
  shared_drive_id:
  credentials:
gmail:
  app_password:
  user_email:
external:
  vernon:
    userno: test
    passno: test

dify:
  base_url:
  api_key:
  knowledge_base_api_key:
  dataset_id:
  document_id: