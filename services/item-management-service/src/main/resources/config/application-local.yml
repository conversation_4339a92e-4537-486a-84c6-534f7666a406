host:
  db_server: localhost:5432
spring:
  config:
    activate:
      on-profile: local
  datasource:
    username: item_management_user
    password: mercaso
    url: jdbc:postgresql://${host.db_server}/item_management_service
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: false
  cloud:
    vault:
      enabled: false
  ai:
    openai:
      api-key: xxx
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.4

otel:
  exporter:
    otlp:
      endpoint: http://localhost:4317

security:
  enable-method-security: false
  public-paths:
    - /**

finale:
  accountPathComponent: mercasosandbox
  token:

shopify:
  host:
  access_token:
  query_product_url:
  create_product_url:
  modify_product_url:
  delete_product_url:
  modify_product_image_url:
  remove_product_image_url:
  create_product_image_attach_to_product_variants_url:
  create_product_meta_field_url:
  delete_product_meta_field_url:
  query_product_meta_field_url:
  modify_product_meta_field_url:
  modify_product_inventory_item_url:
  set_product_channel_url:

ims:
  document_url: http://localhost:8080/v1/document/public-paths/
  alert:
    alert_slack_hook:

googleDriver:
  shared_drive_id:
  credentials:
gmail:
  app_password:
  user_email:

springdoc:
  swagger-ui:
    enabled: true

mercaso:
  featureflags:
    sdk: test
  document:
    operations:
      storage:
        bucket-name: mercaso-application-dev
external:
  vernon:
    userno: test
    passno: test

dify:
  base_url: http://*************/v1
  api_key: app-kfjuo1radpYdCtoqOvYiOqKw
  knowledge_base_api_key: dataset-eZkcd9q61dhnHScM9XKVvyHf
  dataset_id: f8907269-8a1b-419d-b2fd-5bc33348517c
  document_id: 28e2d308-7e6d-4028-b12f-53e596e00854