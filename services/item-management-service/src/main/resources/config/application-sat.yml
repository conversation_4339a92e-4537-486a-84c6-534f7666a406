spring:
  config:
    import: vault://
    activate:
      on-profile: sat
security:
  public-paths:
    - /v1/document/public-paths/**
    - /v1/document/coming_soon.jpg

shopify:
  host: https://sat-mercaso.myshopify.com

springdoc:
  swagger-ui:
    enabled: true

resilience4j.ratelimiter:
  instances:
    syncShopify:
      limitForPeriod: 1
      limitRefreshPeriod: 5s
      timeoutDuration: 60s
    invokeShopifyApi:
      limitForPeriod: 1
      limitRefreshPeriod: 1s
      timeoutDuration: 60s
ims:
  saas_url: https://saas-sat.mercaso.store
  alert:
    alert_slack_hook: https://hooks.slack.com/triggers/T02AVL4UJG4/*************/371803a2b90038a208bb743fbbbc59a6
finale:
  accountPathComponent: mercasosandbox
external:
  seven_star:
    from_email: <EMAIL>