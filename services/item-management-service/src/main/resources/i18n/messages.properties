000000=API exception
000400={0} {1} not found
001001=Brand is not found.
001002=Brand already exist.
001003=Invalid brand name.
001004=Brand has items.
002001=Category is not found.
002002=Category already exist.
002003=Category relationship is empty.
002004=Category should be not null.
002005=Category has items.
003001=Attribute is not found.
003002=Attribute Enum Value is not found.
003003=Attribute Group is not found.
004001=Company is not found.
005001=Item is not found.
005002=Item already exist.
005003=Invalid SKU number.
005004=Item binding photo error.
005005=Invalid bottle unit.
005006=Item list search invalid filter key.
005007=Item list search json processing exception.
006001=Item Promo Price is not found.
006002=Item generate promo photo error.
007001=Item Reg Price is not found.
007002=Item Reg Price already exist.
007003=Item Reg Price has been changed.
007004=New price is different with group price.
007005=Item Reg Price already bound to a price group.
008001=Location is not found.
009001=Vendor is not found.
009002=Vendor already exist.
009003=Invalid backup vendor.
009004=Invalid primary po vendor.
009005=Invalid primary jit vendor.
010001=Vendor Item is not found.
010002=Primary Vendor Item not found.
010003=Vendor Item already exist.
010004=Backup Vendor Item not found.
011001=Plytix other data is not found.
012001=Item Adjustment is not found.
012002=Item Adjustment status has been changed.
013001=Item Adjustment Request is not found.
014001=Plytix Get token failed.
014002=Plytix Create item failed.
014003=Plytix Invoke API timeout.
014004=Plytix Get token expiration.
014005=Plytix Modify item failed.
014006=Plytix API error.
014007=Plytix Env error.
014008=Plytix Jump to env error.
014009=Plytix Get product id error.
014010=Plytix 401 Unauthorized.
015001=Shopify Request param error.
015002=Shopify Modify must has gid.
015003=Shopify Request error.
015004=Shopify Invoke API timeout.
015005=Shopify Create error.
016001=Finale Change stock failed.
016002=Finale API error.
016003=Finale Login error.
016004=Finale Create supplier failed.
016005=Finale Query suppliers failed.
016006=Finale update suppliers item failed.
016007=Finale Query Purchase Order failed.
016008=Finale Create item failed.
016009=Finale check sku number failed.
016010=Finale get product failed.
016011=Finale change status failed.
017001=Item Sync Info is not found.
018001=Another instance is already generating the merchandise report.
018002=Get Change Result Merchandise Report file error.
018003=Failed to generate the merchandise report.
019001=Vendor PO invoice item is not found.
019002=Vendor PO invoice is not found.
020001=Unexpected error.
022001=Invoke aws analyze Expense Error.
026002=Item price group already exists.
030001=Dify API call failed.
030003=Get recommended categories error.