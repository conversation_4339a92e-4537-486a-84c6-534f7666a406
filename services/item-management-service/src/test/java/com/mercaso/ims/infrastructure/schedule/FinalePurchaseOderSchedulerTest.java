package com.mercaso.ims.infrastructure.schedule;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.utils.finale.FinaleOrderCollectionDtoUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionDtoUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class FinalePurchaseOderSchedulerTest {

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private EntityManagerFactory managerFactory;

    @Mock
    private VernonAdaptor vernonAdaptor;

    @Mock
    private VendorService vendorService;

    @Mock
    private VendorItemService vendorItemService;
    @Mock
    private ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @Mock
    private ItemCostCollectionService itemCostCollectionService;

    @Mock
    private VendorItemApplicationService vendorItemApplicationService;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @Mock
    DocumentApplicationService documentApplicationService;

    @InjectMocks
    FinalePurchaseOderScheduler finalePurchaseOderScheduler;
    private EntityManager entityManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        entityManager = Mockito.mock(EntityManager.class);

        when(managerFactory.createEntityManager()).thenReturn(entityManager);
    }


    @Test
    void testSyncFinalePurchaseOrders() {
        UUID id = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        Vendor vendor = VendorUtil.buildVendor(id);
        ItemCostCollectionDto itemCostCollectionDto = ItemCostCollectionDtoUtil.buildItemCostCollectionDto();

        FinaleOrderCollectionDto sampleOrderCollection = FinaleOrderCollectionDtoUtil.createSampleOrderCollection();

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(pgAdvisoryLock.unLock(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(managerFactory.createEntityManager()).thenReturn(entityManager);
        when(vendorService.findByFinaleId(anyString())).thenReturn(vendor);
        when(itemCostCollectionService.findByVendorId(any(UUID.class))).thenReturn(List.of(itemCostCollection));
        when(itemCostCollectionApplicationService.create(any(CreateItemCostCollectionCommand.class))).thenReturn(
            itemCostCollectionDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(documentApplicationService.uploadFileContent(any(byte[].class), anyString(), anyBoolean())).thenReturn(new DocumentResponse("signedUrl", "name"));
        when(finaleExternalApiAdaptor.queryPurchaseOrdersByFilter(anyInt())).thenReturn(sampleOrderCollection);

        finalePurchaseOderScheduler.syncFinalePurchaseOrders();

        verify(itemCostCollectionApplicationService, times(3)).create(any());

    }
}