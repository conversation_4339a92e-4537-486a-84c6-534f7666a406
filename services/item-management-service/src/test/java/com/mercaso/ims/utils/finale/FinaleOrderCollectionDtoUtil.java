package com.mercaso.ims.utils.finale;

import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto.OrderItem;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto.OrderRole;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto.StatusHistory;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class FinaleOrderCollectionDtoUtil {

    /**
     * Create a sample FinaleOrderCollectionDto for testing purposes
     */
    public static FinaleOrderCollectionDto createSampleOrderCollection() {
        return FinaleOrderCollectionDto.builder()
            .orderId(Arrays.asList("101540", "101541", "101502"))
            .orderUrl(Arrays.asList(
                "/mercaso/api/order/101540",
                "/mercaso/api/order/101541", 
                "/mercaso/api/order/101502"
            ))
            .orderTypeId(Arrays.asList("PURCHASE_ORDER", "PURCHASE_ORDER", "PURCHASE_ORDER"))
            .lastUpdatedDate(Arrays.asList(
                LocalDateTime.parse("2025-05-28T14:50:32"),
                LocalDateTime.parse("2025-05-28T14:59:42"),
                LocalDateTime.parse("2025-05-28T15:49:49")
            ))
            .createdDate(Arrays.asList(
                LocalDateTime.parse("2025-05-27T13:17:32"),
                LocalDateTime.parse("2025-05-27T13:20:26"),
                LocalDateTime.parse("2025-05-22T14:55:45")
            ))
            .settlementTermId(Arrays.asList("NET_15_3_15", "NET_15_3_15", "NET_10_2_M"))
            .fulfillmentId(Arrays.asList("Delivery", "Delivery", null))
            .orderDate(Arrays.asList(
                LocalDateTime.parse("2025-05-27T19:00:00"),
                LocalDateTime.parse("2025-05-27T19:00:00"),
                LocalDateTime.parse("2025-05-22T19:00:00")
            ))
            .receiveDate(Arrays.asList(
                LocalDateTime.parse("2025-05-28T19:00:00"),
                LocalDateTime.parse("2025-05-28T19:00:00"),
                LocalDateTime.parse("2025-05-29T19:00:00")
            ))
            .orderItemListTotal(Arrays.asList(
                BigDecimal.valueOf(39119.56),
                BigDecimal.valueOf(34226.33),
                BigDecimal.valueOf(5790.00)
            ))
            .statusId(Arrays.asList("ORDER_COMPLETED", "ORDER_COMPLETED", "ORDER_COMPLETED"))
            .destinationFacilityUrl(Arrays.asList(
                "/mercaso/api/facility/101172",
                "/mercaso/api/facility/101172",
                "/mercaso/api/facility/101172"
            ))
            .orderItemList(Arrays.asList(
                    Collections.singletonList(createSampleOrderItem()),
                    Collections.singletonList(createSampleOrderItem()),
                    Collections.singletonList(createSampleOrderItem())
            ))
            .orderAdjustmentList(Arrays.asList(
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList()
            ))
            .orderRoleList(Arrays.asList(
                    Collections.singletonList(createSampleOrderRole()),
                    Collections.singletonList(createSampleOrderRole()),
                    Collections.singletonList(createSampleOrderRole())
            ))
            .contentList(Arrays.asList(
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList()
            ))
            .userFieldDataList(Arrays.asList(
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList()
            ))
            .shipmentUrlList(Arrays.asList(
                    List.of("/mercaso/api/shipment/227331"),
                    List.of("/mercaso/api/shipment/227344"),
                    List.of("/mercaso/api/shipment/227379")
            ))
            .invoiceUrlList(Arrays.asList(
                    List.of("/mercaso/api/invoice/101639"),
                    List.of("/mercaso/api/invoice/101640"),
                    List.of("/mercaso/api/invoice/101641")
            ))
            .connectionRelationUrlList(Arrays.asList(
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList()
            ))
            .statusIdHistoryList(Arrays.asList(
                    Collections.singletonList(createSampleStatusHistory()),
                    Collections.singletonList(createSampleStatusHistory()),
                    Collections.singletonList(createSampleStatusHistory())
            ))
            .build();
    }

    /**
     * Create a sample order item for testing
     */
    private static OrderItem createSampleOrderItem() {
        return OrderItem.builder()
            .orderItemUrl("/mercaso/api/order/101540/orderItem/00000001")
            .reserveUrl("/mercaso/api/order/101540/orderItem/00000001/reserve")
            .productId("AM61721")
            .productUrl("/mercaso/api/product/AM61721")
            .unitPrice(BigDecimal.valueOf(27.03))
            .quantity(44)
            .build();
    }

    /**
     * Create a sample order role for testing
     */
    private static OrderRole createSampleOrderRole() {
        return OrderRole.builder()
            .roleTypeId("SUPPLIER")
            .partyId("100007")
            .build();
    }

    /**
     * Create a sample status history for testing
     */
    private static StatusHistory createSampleStatusHistory() {
        return StatusHistory.builder()
            .statusId(null)
            .txStamp(1748351852L)
            .userLoginUrl("/mercaso/api/userlogin/Sindiy")
            .build();
    }
}
