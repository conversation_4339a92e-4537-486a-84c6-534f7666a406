package com.mercaso.ims.domain.itemvendorrebate;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ItemVendorRebate domain entity
 */
class ItemVendorRebateTest {

    @Test
    void testIsActiveOn_WithinDateRange() {
        // Given
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 31);
        LocalDate testDate = LocalDate.of(2024, 6, 15);
        
        ItemVendorRebate rebate = createTestRebate(startDate, endDate);
        
        // When & Then
        assertTrue(rebate.isActiveOn(testDate));
    }

    @Test
    void testIsActiveOn_BeforeStartDate() {
        // Given
        LocalDate startDate = LocalDate.of(2024, 6, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 31);
        LocalDate testDate = LocalDate.of(2024, 5, 31);
        
        ItemVendorRebate rebate = createTestRebate(startDate, endDate);
        
        // When & Then
        assertFalse(rebate.isActiveOn(testDate));
    }

    @Test
    void testIsActiveOn_AfterEndDate() {
        // Given
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 6, 30);
        LocalDate testDate = LocalDate.of(2024, 7, 1);
        
        ItemVendorRebate rebate = createTestRebate(startDate, endDate);
        
        // When & Then
        assertFalse(rebate.isActiveOn(testDate));
    }

    @Test
    void testIsActiveOn_IndefiniteRebate() {
        // Given
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate testDate = LocalDate.of(2024, 12, 31);
        
        ItemVendorRebate rebate = createTestRebate(startDate, null);
        
        // When & Then
        assertTrue(rebate.isActiveOn(testDate));
    }

    @Test
    void testIsActiveOn_NullDate() {
        // Given
        ItemVendorRebate rebate = createTestRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
        
        // When & Then
        assertFalse(rebate.isActiveOn(null));
    }

    @Test
    void testCalculateRebateAmount() {
        // Given
        BigDecimal rebatePerUnit = new BigDecimal("5.00");
        BigDecimal quantity = new BigDecimal("10");
        BigDecimal expectedAmount = new BigDecimal("50.00");
        
        ItemVendorRebate rebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(UUID.randomUUID())
                .vendorId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .rebatePerUnit(rebatePerUnit)
                .build();
        
        // When
        BigDecimal actualAmount = rebate.calculateRebateAmount(quantity);
        
        // Then
        assertEquals(0, expectedAmount.compareTo(actualAmount));
    }

    @Test
    void testCalculateRebateAmount_NullQuantity() {
        // Given
        ItemVendorRebate rebate = createTestRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
        
        // When
        BigDecimal actualAmount = rebate.calculateRebateAmount(null);
        
        // Then
        assertEquals(BigDecimal.ZERO, actualAmount);
    }

    @Test
    void testCalculateRebateAmount_NullRebatePerUnit() {
        // Given
        ItemVendorRebate rebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(UUID.randomUUID())
                .vendorId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .rebatePerUnit(null)
                .build();
        
        // When
        BigDecimal actualAmount = rebate.calculateRebateAmount(new BigDecimal("10"));
        
        // Then
        assertEquals(BigDecimal.ZERO, actualAmount);
    }

    @Test
    void testIsExpired() {
        // Given
        LocalDate pastDate = LocalDate.now().minusDays(1);
        ItemVendorRebate rebate = createTestRebate(LocalDate.of(2024, 1, 1), pastDate);
        
        // When & Then
        assertTrue(rebate.isExpired());
    }

    @Test
    void testIsNotStarted() {
        // Given
        LocalDate futureDate = LocalDate.now().plusDays(1);
        ItemVendorRebate rebate = createTestRebate(futureDate, null);
        
        // When & Then
        assertTrue(rebate.isNotStarted());
    }

    private ItemVendorRebate createTestRebate(LocalDate startDate, LocalDate endDate) {
        return ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(UUID.randomUUID())
                .vendorId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(new BigDecimal("5.00"))
                .build();
    }
}
