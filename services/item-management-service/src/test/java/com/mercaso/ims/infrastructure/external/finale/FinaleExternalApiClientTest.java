package com.mercaso.ims.infrastructure.external.finale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.*;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class FinaleExternalApiClientTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private FinaleExternalApiClient finaleExternalApiClient;

    private static final String TEST_TOKEN = "test-token";
    private static final String TEST_CREATE_VENDOR_URL = "https://api.finale.com/vendors";
    private static final String TEST_GRAPHQL_URL = "https://api.finale.com/graphql";
    private static final String TEST_QUERY_VENDOR_URL = "https://api.finale.com/query-vendors";
    private static final String TEST_PRODUCT_URL = "https://api.finale.com/products/";
    private static final String TEST_GET_PURCHASE_ORDER_URL = "https://api.finale.com/purchase-orders/";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(finaleExternalApiClient, "token", TEST_TOKEN);
        ReflectionTestUtils.setField(finaleExternalApiClient, "createVendorUrl", TEST_CREATE_VENDOR_URL);
        ReflectionTestUtils.setField(finaleExternalApiClient, "graphqlUrl", TEST_GRAPHQL_URL);
        ReflectionTestUtils.setField(finaleExternalApiClient, "queryVendorUrl", TEST_QUERY_VENDOR_URL);
        ReflectionTestUtils.setField(finaleExternalApiClient, "productUrl", TEST_PRODUCT_URL);
        ReflectionTestUtils.setField(finaleExternalApiClient, "getPurchaseOrderUrl", TEST_GET_PURCHASE_ORDER_URL);
    }

    @Test
    void queryFinaleVendors_Success_ReturnsVendorsResult() throws IOException {
        // Arrange
        String mockResponseBody = "{\"partyId\":[\"123\"],\"groupName\":[\"Test Vendor\"]}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        QueryFinaleVendorsResultDto result = finaleExternalApiClient.queryFinaleVendors();

        // Assert
        assertNotNull(result);
        assertEquals(List.of("123"), result.getPartyId());
        assertEquals(List.of("Test Vendor"), result.getGroupName());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryFinaleVendors_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryFinaleVendors());

        assertEquals(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryFinaleVendors_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryFinaleVendors());

        assertEquals(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createVendor_Success_ReturnsFinaleVendorDto() throws IOException {
        // Arrange
        String vendorName = "Test Vendor";
        String mockResponseBody = "{\"partyId\":\"123\",\"groupName\":\"Test Vendor\"}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        FinaleVendorDto result = finaleExternalApiClient.createVendor(vendorName);

        // Assert
        assertNotNull(result);
        assertEquals("123", result.getPartyId());
        assertEquals("Test Vendor", result.getGroupName());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createVendor_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String vendorName = "Test Vendor";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.createVendor(vendorName));

        assertEquals(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createVendor_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String vendorName = "Test Vendor";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.createVendor(vendorName));

        assertEquals(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateVendorItem_Success_CompletesWithoutException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        UpdateSupplierItemRequestDto requestDto = UpdateSupplierItemRequestDto.builder()
            .productId(skuNumber)
            .supplierList(Collections.emptyList())
            .build();
        
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{}");

        // Act & Assert
        assertDoesNotThrow(() -> finaleExternalApiClient.updateVendorItem(skuNumber, StatusId.PRODUCT_ACTIVE.getAction(), requestDto));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateVendorItem_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        UpdateSupplierItemRequestDto requestDto = UpdateSupplierItemRequestDto.builder().build();
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        String action = StatusId.PRODUCT_ACTIVE.getAction();
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateVendorItem(skuNumber, action, requestDto));

        assertEquals(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateVendorItem_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        UpdateSupplierItemRequestDto requestDto = UpdateSupplierItemRequestDto.builder().build();
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        String action = StatusId.PRODUCT_ACTIVE.getAction();
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateVendorItem(skuNumber, action, requestDto));

        assertEquals(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrders_Success_ReturnsQueryResult() throws IOException {
        // Arrange
        int length = 30;
        String mockResponseBody = "{\"data\":{\"orderViewConnection\":{\"edges\":[]}}}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        QueryFinalePurchaseOrderResultDto result = finaleExternalApiClient.queryPurchaseOrders(length);

        // Assert
        assertNotNull(result);
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrders_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        int length = 30;
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryPurchaseOrders(length));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrders_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        int length = 30;
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryPurchaseOrders(length));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getPurchaseOrder_Success_ReturnsPurchaseOrderDto() throws IOException {
        // Arrange
        String orderId = "PO-123";
        String mockResponseBody = "{\"orderId\":\"PO-123\",\"statusId\":\"COMPLETED\"}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        FinalePurchaseOrderDto result = finaleExternalApiClient.getPurchaseOrder(orderId);

        // Assert
        assertNotNull(result);
        assertEquals("PO-123", result.getOrderId());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getPurchaseOrder_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String orderId = "PO-123";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.getPurchaseOrder(orderId));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getPurchaseOrder_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String orderId = "PO-123";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.getPurchaseOrder(orderId));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createFinaleItem_Success_CompletesWithoutException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{}");

        // Act & Assert
        assertDoesNotThrow(() -> finaleExternalApiClient.createFinaleItem(skuNumber, StatusId.PRODUCT_INACTIVE.name()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createFinaleItem_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.createFinaleItem(skuNumber, StatusId.PRODUCT_INACTIVE.name()));

        assertEquals(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createFinaleItem_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.createFinaleItem(skuNumber, StatusId.PRODUCT_INACTIVE.name()));

        assertEquals(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void skuIsUnique_ReturnsTrue_WhenSkuIsUnique() throws IOException {
        // Arrange
        String skuNumber = "UNIQUE-SKU-001";
        String mockResponseBody = "{\"data\":{\"productViewConnection\":{\"summary\":{\"metrics\":{\"count\":[0]}}}}}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        boolean result = finaleExternalApiClient.skuIsUnique(skuNumber);

        // Assert
        assertTrue(result);
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void skuIsUnique_ReturnsFalse_WhenSkuExists() throws IOException {
        // Arrange
        String skuNumber = "EXISTING-SKU-001";
        String mockResponseBody = "{\"data\":{\"productViewConnection\":{\"summary\":{\"metrics\":{\"count\":[1]}}}}}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        boolean result = finaleExternalApiClient.skuIsUnique(skuNumber);

        // Assert
        assertFalse(result);
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void skuIsUnique_ReturnsTrue_WhenCountArrayIsEmpty() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String mockResponseBody = "{\"data\":{\"productViewConnection\":{\"summary\":{\"metrics\":{\"count\":[]}}}}}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        boolean result = finaleExternalApiClient.skuIsUnique(skuNumber);

        // Assert
        assertTrue(result);
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void skuIsUnique_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(500);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.skuIsUnique(skuNumber));

        assertEquals(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void skuIsUnique_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.skuIsUnique(skuNumber));

        assertEquals(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getFinaleProduct_Success_ReturnsFinaleProductDto() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String mockResponseBody = "{\"productId\":\"TEST-SKU-001\",\"internalName\":\"Test Product\"}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        FinaleProductDto result = finaleExternalApiClient.getFinaleProduct(skuNumber, StatusId.PRODUCT_ACTIVE.getAction());

        // Assert
        assertNotNull(result);
        assertEquals("TEST-SKU-001", result.getProductId());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getFinaleProduct_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);

        // Act & Assert
        String action = StatusId.PRODUCT_ACTIVE.getAction();
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.getFinaleProduct(skuNumber, action));

        assertEquals(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void getFinaleProduct_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        String action = StatusId.PRODUCT_ACTIVE.getAction();
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.getFinaleProduct(skuNumber, action));

        assertEquals(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateItemStatus_Success_CompletesWithoutException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String status = "ACTIVE";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        // Note: Only response.body() is called in the successful path, not responseBody.string()

        // Act & Assert
        assertDoesNotThrow(() -> finaleExternalApiClient.updateItemStatus(skuNumber, status));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateItemStatus_404Response_CallsCreateFinaleItemForDeactivate() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String status = "deactivate"; // Use "deactivate" to test the other supported status

        // First call (updateItemStatus) returns 404
        Response firstResponse = mock(Response.class);
        when(firstResponse.isSuccessful()).thenReturn(false);
        when(firstResponse.code()).thenReturn(404);

        // Second call (createFinaleItemSyncInternal) succeeds
        Response secondResponse = mock(Response.class);
        when(secondResponse.isSuccessful()).thenReturn(true);
        when(secondResponse.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{}");

        when(httpClient.execute(any(Request.class)))
            .thenReturn(firstResponse)
            .thenReturn(secondResponse);

        // Act & Assert - Should throw exception even after creating item (for retry mechanism)
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateItemStatus(skuNumber, status));

        assertEquals(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED.getCode(), exception.getCode());

        // Verify that both updateItemStatus and createFinaleItemSyncInternal were called
        verify(httpClient, times(2)).execute(any(Request.class));
    }

    @Test
    void updateItemStatus_404Response_UnsupportedStatus_ThrowsException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String status = "ACTIVE"; // Unsupported status that doesn't match "activate" or "deactivate"

        // First call (updateItemStatus) returns 404
        Response firstResponse = mock(Response.class);
        when(firstResponse.isSuccessful()).thenReturn(false);
        when(firstResponse.code()).thenReturn(404);

        when(httpClient.execute(any(Request.class)))
            .thenReturn(firstResponse);

        // Act & Assert - Should throw exception even for unsupported status (for retry mechanism)
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateItemStatus(skuNumber, status));

        assertEquals(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED.getCode(), exception.getCode());

        // Verify that only the first call (updateItemStatus) was made, no createFinaleItem call for unsupported status
        verify(httpClient, times(1)).execute(any(Request.class));
    }

    @Test
    void updateItemStatus_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String status = "ACTIVE";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(500);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateItemStatus(skuNumber, status));

        assertEquals(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateItemStatus_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String status = "ACTIVE";
        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.updateItemStatus(skuNumber, status));

        assertEquals(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrdersByFilter_Success_ReturnsFinaleOrderCollectionDto() throws IOException {
        // Arrange
        QueryFinalePurchaseOrderRequestDto requestDto = spy(new QueryFinalePurchaseOrderRequestDto());
        doReturn("dGVzdA==").when(requestDto).getBase64EncodedFilter(); // Mock the problematic method

        String mockResponseBody = "{}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        FinaleOrderCollectionDto result = finaleExternalApiClient.queryPurchaseOrdersByFilter(requestDto);

        // Assert
        assertNotNull(result);
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrdersByFilter_HttpFailure_ThrowsImsBusinessException() throws IOException {
        // Arrange
        QueryFinalePurchaseOrderRequestDto requestDto = spy(new QueryFinalePurchaseOrderRequestDto());
        doReturn("dGVzdA==").when(requestDto).getBase64EncodedFilter(); // Mock the problematic method

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(500);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryPurchaseOrdersByFilter(requestDto));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryPurchaseOrdersByFilter_IOException_ThrowsImsBusinessException() throws IOException {
        // Arrange
        QueryFinalePurchaseOrderRequestDto requestDto = spy(new QueryFinalePurchaseOrderRequestDto());
        doReturn("dGVzdA==").when(requestDto).getBase64EncodedFilter(); // Mock the problematic method

        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> finaleExternalApiClient.queryPurchaseOrdersByFilter(requestDto));

        assertEquals(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED.getCode(), exception.getCode());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void buildCreateVendorRequestDto_CreatesCorrectRequest() {
        // Arrange
        String vendorName = "Test Vendor";

        // Act - Using reflection to test the private method
        CreateVendorRequestDto result = (CreateVendorRequestDto) ReflectionTestUtils.invokeMethod(
            finaleExternalApiClient, "buildCreateVendorRequestDto", vendorName);

        // Assert
        assertNotNull(result);
        assertEquals(vendorName, result.getGroupName());
        assertEquals("PARTY_ENABLED", result.getStatusId());
        assertEquals(List.of("SUPPLIER"), result.getRoleTypeIdList());
    }

    @Test
    void queryFinaleVendors_VerifyRequestConstruction() throws IOException {
        // Arrange
        String mockResponseBody = "{\"partyId\":[],\"groupName\":[]}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        finaleExternalApiClient.queryFinaleVendors();

        // Assert - Verify the request was constructed correctly
        verify(httpClient).execute(argThat(request -> {
            assertEquals(TEST_QUERY_VENDOR_URL, request.url().toString());
            assertEquals("GET", request.method());
            assertEquals(TEST_TOKEN, request.header("Authorization"));
            assertEquals("application/json", request.header("Content-Type"));
            return true;
        }));
    }

    @Test
    void createVendor_VerifyRequestConstruction() throws IOException {
        // Arrange
        String vendorName = "Test Vendor";
        String mockResponseBody = "{\"partyId\":\"123\",\"groupName\":\"Test Vendor\"}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        finaleExternalApiClient.createVendor(vendorName);

        // Assert - Verify the request was constructed correctly
        verify(httpClient).execute(argThat(request -> {
            assertEquals(TEST_CREATE_VENDOR_URL, request.url().toString());
            assertEquals("POST", request.method());
            assertEquals(TEST_TOKEN, request.header("Authorization"));
            assertEquals("application/json", request.header("Content-Type"));
            assertNotNull(request.body());
            return true;
        }));
    }

    @Test
    void skuIsUnique_VerifyGraphQLRequestConstruction() throws IOException {
        // Arrange
        String skuNumber = "TEST-SKU-001";
        String mockResponseBody = "{\"data\":{\"productViewConnection\":{\"summary\":{\"metrics\":{\"count\":[0]}}}}}";
        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);

        // Act
        finaleExternalApiClient.skuIsUnique(skuNumber);

        // Assert - Verify the GraphQL request was constructed correctly
        verify(httpClient).execute(argThat(request -> {
            assertEquals(TEST_GRAPHQL_URL, request.url().toString());
            assertEquals("POST", request.method());
            assertEquals(TEST_TOKEN, request.header("Authorization"));
            assertEquals("application/json", request.header("Content-Type"));
            assertNotNull(request.body());
            return true;
        }));
    }
}
