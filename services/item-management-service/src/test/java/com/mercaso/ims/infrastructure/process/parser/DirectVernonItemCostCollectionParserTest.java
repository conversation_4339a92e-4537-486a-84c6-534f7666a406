package com.mercaso.ims.infrastructure.process.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import com.mercaso.ims.utils.finale.FinaleOrderCollectionDtoUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DirectVernonItemCostCollectionParserTest {

    @Mock
    private ItemCostCollectionService itemCostCollectionService;

    @Mock
    private FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @Mock
    private DocumentApplicationService documentApplicationService;

    @InjectMocks
    private DirectVernonItemCostCollectionParser directVernonItemCostCollectionParser;

    @Test
    void testParse_Success() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");


        byte[] sampleOrderCollectionJsonBytes = getSampleOrderCollectionJsonBytes();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(sampleOrderCollectionJsonBytes);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("AM61721", result.getFirst().getVendorSkuNumber());
    }

    @Test
    void testParse_NullItemCostCollection() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(null);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_InvalidSource() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.VERNON_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_InvalidType() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.CSV_FILE);

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_NullPurchaseOrder() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");


        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(null);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_WithInvalidPackingString() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");
        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testParse_EmptyOrderItemList() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        byte[] sampleOrderCollectionJsonBytes = getSampleOrderCollectionJsonBytes();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(sampleOrderCollectionJsonBytes);

        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
    }

    @Test
    void testIsSupported_True() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isSupported("VERNON", ItemCostCollectionSources.FINALE_PURCHASE_ORDER);

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsSupported_False() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isSupported("VERNON", ItemCostCollectionSources.VERNON_ORDER);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsUpdateAvailability() {
        // Act
        boolean result = directVernonItemCostCollectionParser.isUpdateAvailability();

        // Assert
        assertFalse(result);
    }

    @Test
    void testParse_WithDecimalConversionFactor() {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        byte[] sampleOrderCollectionJsonBytes = getSampleOrderCollectionJsonBytes();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(sampleOrderCollectionJsonBytes);


        // Act
        List<ItemCostCollectionItemParsingResultDto> result = directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        assertEquals(1, result.size());
        assertEquals("AM61721", result.getFirst().getVendorSkuNumber());
        assertEquals(new BigDecimal("27.03"), result.getFirst().getCost());
    }

    @Test
    void testParse_VerifyServiceCalls()  {
        // Arrange
        UUID itemCostCollectionId = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setSource(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        itemCostCollection.setType(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER);
        itemCostCollection.setVendorCollectionNumber("PO123");

        byte[] sampleOrderCollectionJsonBytes = getSampleOrderCollectionJsonBytes();

        when(itemCostCollectionService.findById(itemCostCollectionId)).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(sampleOrderCollectionJsonBytes);

        // Act
        directVernonItemCostCollectionParser.parse(itemCostCollectionId);

        // Assert
        verify(itemCostCollectionService).findById(itemCostCollectionId);
    }

    private byte[] getSampleOrderCollectionJsonBytes () {
        FinaleOrderCollectionDto sampleOrderCollection = FinaleOrderCollectionDtoUtil.createSampleOrderCollection();

        String orderJson = SerializationUtils.serialize(sampleOrderCollection.getAllOrders().getFirst());
        return orderJson.getBytes(StandardCharsets.UTF_8);
    }
}
